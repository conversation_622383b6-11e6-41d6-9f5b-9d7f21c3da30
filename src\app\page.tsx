"use client";

import allocations from "@/app/assets/allocations.png";
import analyzeIcon from "@/app/assets/analiza-dividende.png";
import personalizeIcon from "@/app/assets/personalizeaza-info.svg";
import { Footer } from "@/components/footer";
import { Header } from "@/components/header";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen theme-transition text-white">
      <Header variant="landing" />

      <section
        className="pt-36 pb-16 px-4 md:px-8 lg:px-52 min-h-[750px] relative"
        style={{
          backgroundImage: "url('/bg-section.svg')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="max-w-8xl mx-auto mt-8 md:mt-32">
          <div className="flex flex-col lg:flex-row justify-center lg:justify-evenly items-center lg:items-start gap-12 lg:gap-2">
            <div className="text-center lg:text-left max-w-lg lg:max-w-none">
              <h1 className="text-3xl md:text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                Simplifică-ți investițiile
              </h1>
              <p className="text-base md:text-lg lg:text-xl mb-8 text-gray-200 leading-relaxed">
                Conectează-te automat cu brokerul tău și urmărește-ți
                portofoliul în detaliu.
              </p>
              <Link href={"/dashboard"}>
                <Button
                  size="lg"
                  className="bg-portavio-orange hover:bg-portavio-orange-hover text-white font-medium px-8 py-3 rounded-lg text-lg cursor-pointer"
                >
                  Descoperă Portavio
                </Button>
              </Link>
            </div>

            <div className="relative flex-shrink-0">
              <Image
                src={allocations}
                alt="Allocations"
                height={345}
                width={360}
                className="w-[280px] md:w-[320px] lg:w-[360px] h-auto"
              />
              {/* <Image
                src={barchart}
                alt="BarChart"
                height={180}
                width={190}
                className="absolute top-[-30px] md:top-[-40px] lg:top-[-50px] right-[-60px] md:right-[-80px] lg:right-[-92px] w-[140px] md:w-[170px] lg:w-[190px] h-auto"
              /> */}
            </div>
          </div>
        </div>

        <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-b from-transparent via-black/50 to-black pointer-events-none"></div>
      </section>

      {/* <section className="py-16 bg-black">
        <div className="max-w-4xl mx-auto text-center px-6">
          <h2 className="text-3xl font-semibold mb-3 text-white">
            Să investești nu a fost niciodată mai simplu
          </h2>
          <h2 className="text-2xl font-normal mb-12 text-white">
            prin integrarea automată cu broker-ul tău
          </h2>
          <div className="flex justify-center items-center gap-20 flex-wrap">
            <Image src={tradevilleIcon} alt="TradeVille" height={48} />
            <Image src={ibkrIcon} alt="Interactive Brokers" height={48} />
            <Image src={xtbIcon} alt="XTB" height={48} />
          </div>
        </div>
      </section> */}

      <section className="py-20 px-6 bg-black">
        <div className="container w-full mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            <div className="order-2 lg:order-1">
              <Image
                src={analyzeIcon}
                alt="Analiza Dividende"
                height={480}
                width={684}
                className="w-full h-auto"
              />
            </div>

            <div className="order-1 lg:order-2">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4 text-white">
                Mai mult control, decizii mai rapide
              </h3>
              <p className="text-lg text-gray-300 leading-relaxed">
                Zeci de grafice, analize și date relevante te așteaptă.
              </p>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="order-1 lg:order-1">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4 text-white">
                Alegi ce date sunt cele mai importante pentru tine
              </h3>
              <p className="text-lg text-gray-300 leading-relaxed">
                Interfață personalizabilă pentru control absolut.
              </p>
            </div>
            <div className="order-2 lg:order-2">
              <Image
                src={personalizeIcon}
                alt="Personalizeaza Info"
                height={480}
                width={684}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 px-6 bg-black text-white">
        <div className="max-w-4xl mx-auto text-center">
          {/* <h3 className="text-2xl md:text-3xl font-semibold mb-12 text-white">
            Ești la curent cu tot ce se întâmplă în lumea financiară
          </h3> */}
          {/* <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 md:gap-8 mb-12">
            {[
              {
                name: "Știri",
                icon: <Image src={newsIcon} alt="NewsIcon" />,
              },
              {
                name: "Forum",
                icon: <Image src={forumIcon} alt="ForumIcon" />,
              },
              {
                name: "Watchlist",
                icon: <Image src={watchlistIcon} alt="WatchlistIcon" />,
              },
              {
                name: "Calendar dividende",
                icon: <Image src={calendarIcon} alt="CalendarIcon" />,
              },
            ].map((item) => (
              <div
                key={item.name}
                className="flex flex-col items-center gap-3 md:gap-4"
              >
                <div className="bg-[#3E4047] backdrop-blur-sm border border-gray-700/50 p-4 md:p-6 rounded-[50%] transition-colors">
                  <span>{item.icon}</span>
                </div>
                <span className="text-lg md:text-xl font-medium text-gray-300 text-center">
                  {item.name}
                </span>
              </div>
            ))}
          </div> */}
          <Link href="/dashboard">
            <Button
              size="2xl"
              className="bg-portavio-blue hover:bg-[#5AD4FF]/80 text-white px-6 md:px-8 py-3 rounded-lg text-base md:text-lg"
            >
              Încearcă acum
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
}
