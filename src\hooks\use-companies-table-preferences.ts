"use client";

import {
  clearCompaniesTablePreferences,
  getCompaniesTablePreferences,
  saveCompaniesTablePreferences,
  type CompaniesTablePreferences,
} from "@/lib/dashboard-persistence";
import { useCallback, useEffect, useState } from "react";

interface UseCompaniesTablePreferencesOptions {
  userId: string;
  defaultColumnOrder?: string[];
  defaultColumnVisibility?: Record<string, boolean>;
}

export function useCompaniesTablePreferences({
  userId,
  defaultColumnOrder = [],
  defaultColumnVisibility = {},
}: UseCompaniesTablePreferencesOptions) {
  const [columnVisibility, setColumnVisibility] = useState<
    Record<string, boolean>
  >(defaultColumnVisibility);
  const [columnOrder, setColumnOrder] = useState<string[]>(defaultColumnOrder);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load preferences from localStorage on mount
  useEffect(() => {
    if (!userId) {
      setIsLoading(false);
      setIsInitialized(true);
      return;
    }

    try {
      const stored = getCompaniesTablePreferences(userId);

      if (stored) {
        // Use stored preferences as the single source of truth
        setColumnVisibility(stored.columnVisibility || defaultColumnVisibility);
        setColumnOrder(stored.columnOrder || defaultColumnOrder);
      } else {
        // No stored preferences, use defaults
        setColumnVisibility(defaultColumnVisibility);
        setColumnOrder(defaultColumnOrder);
      }
    } catch (error) {
      console.error("Error loading companies table preferences:", error);
      // Fallback to defaults on error
      setColumnVisibility(defaultColumnVisibility);
      setColumnOrder(defaultColumnOrder);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [userId, defaultColumnOrder, defaultColumnVisibility]);

  // Save preferences to localStorage whenever they change (but only after initialization)
  useEffect(() => {
    if (!isInitialized || !userId) return;

    const preferences: CompaniesTablePreferences = {
      columnVisibility,
      columnOrder,
      timestamp: new Date().toISOString(),
    };

    saveCompaniesTablePreferences(userId, preferences);
  }, [userId, columnVisibility, columnOrder, isInitialized]);

  // Wrapped setters that maintain the same API as regular useState
  const updateColumnVisibility = useCallback(
    (visibility: Record<string, boolean>) => {
      setColumnVisibility(visibility);
    },
    []
  );

  const updateColumnOrder = useCallback((order: string[]) => {
    setColumnOrder(order);
  }, []);

  // Utility function to clear all preferences
  const clearPreferences = useCallback(() => {
    if (!userId) return;

    clearCompaniesTablePreferences(userId);
    setColumnVisibility(defaultColumnVisibility);
    setColumnOrder(defaultColumnOrder);
  }, [userId, defaultColumnVisibility, defaultColumnOrder]);

  // Utility function to reset to default values
  const resetToDefaults = useCallback(() => {
    setColumnVisibility(defaultColumnVisibility);
    setColumnOrder(defaultColumnOrder);
  }, [defaultColumnVisibility, defaultColumnOrder]);

  return {
    // Current state
    columnVisibility,
    columnOrder,

    // State setters
    setColumnVisibility: updateColumnVisibility,
    setColumnOrder: updateColumnOrder,

    // Loading state
    isLoading,
    isInitialized,

    // Utility functions
    clearPreferences,
    resetToDefaults,

    // Check if preferences are persisted
    hasPersistedPreferences: userId
      ? getCompaniesTablePreferences(userId) !== null
      : false,
  };
}
