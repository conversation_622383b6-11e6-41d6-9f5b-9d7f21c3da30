"use client";

import { useState, useEffect, useCallback } from "react";
import type { SupportedCurrency } from "@/components/dashboard/currency-selector";
import {
  getDashboardPreferences,
  saveDashboardPreferences,
  clearDashboardPreferences,
  getDefaultDashboardPreferences,
  type DashboardPreferences,
} from "@/lib/dashboard-persistence";

interface UseDashboardPersistenceOptions {
  userId: string;
  initialDisplayCurrency?: SupportedCurrency;
}

export function useDashboardPersistence({
  userId,
  initialDisplayCurrency = "EUR",
}: UseDashboardPersistenceOptions) {
  const [selectedPortfolios, setSelectedPortfolios] = useState<string[]>([]);
  const [displayCurrency, setDisplayCurrency] = useState<SupportedCurrency>(
    initialDisplayCurrency
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load preferences from localStorage on mount
  useEffect(() => {
    if (!userId) {
      setIsLoading(false);
      setIsInitialized(true);
      return;
    }

    try {
      const stored = getDashboardPreferences(userId);

      if (stored) {
        // Use stored preferences as the single source of truth
        setSelectedPortfolios(stored.selectedPortfolios || []);
        setDisplayCurrency(stored.displayCurrency || initialDisplayCurrency);
      } else {
        // No stored preferences, use defaults
        const defaults = getDefaultDashboardPreferences();
        setSelectedPortfolios(defaults.selectedPortfolios);
        setDisplayCurrency(initialDisplayCurrency);
      }
    } catch (error) {
      console.error("Error loading dashboard preferences:", error);
      // Fallback to defaults on error
      const defaults = getDefaultDashboardPreferences();
      setSelectedPortfolios(defaults.selectedPortfolios);
      setDisplayCurrency(initialDisplayCurrency);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [userId, initialDisplayCurrency]);

  // Save preferences to localStorage whenever they change (but only after initialization)
  useEffect(() => {
    if (!isInitialized || !userId) return;

    const preferences: DashboardPreferences = {
      selectedPortfolios,
      displayCurrency,
      timestamp: new Date().toISOString(),
    };

    saveDashboardPreferences(userId, preferences);
  }, [userId, selectedPortfolios, displayCurrency, isInitialized]);

  // Wrapped setters that maintain the same API as regular useState
  const updateSelectedPortfolios = useCallback((portfolios: string[]) => {
    setSelectedPortfolios(portfolios);
  }, []);

  const updateDisplayCurrency = useCallback((currency: SupportedCurrency) => {
    setDisplayCurrency(currency);
  }, []);

  // Utility function to clear all preferences
  const clearPreferences = useCallback(() => {
    if (!userId) return;

    clearDashboardPreferences(userId);
    const defaults = getDefaultDashboardPreferences();
    setSelectedPortfolios(defaults.selectedPortfolios);
    setDisplayCurrency(defaults.displayCurrency);
  }, [userId]);

  // Utility function to reset to default values
  const resetToDefaults = useCallback(() => {
    const defaults = getDefaultDashboardPreferences();
    setSelectedPortfolios(defaults.selectedPortfolios);
    setDisplayCurrency(initialDisplayCurrency);
  }, [initialDisplayCurrency]);

  return {
    // Current state
    selectedPortfolios,
    displayCurrency,

    // State setters
    setSelectedPortfolios: updateSelectedPortfolios,
    setDisplayCurrency: updateDisplayCurrency,

    // Loading state
    isLoading,
    isInitialized,

    // Utility functions
    clearPreferences,
    resetToDefaults,

    // Check if preferences are persisted
    hasPersistedPreferences: userId
      ? getDashboardPreferences(userId) !== null
      : false,
  };
}
