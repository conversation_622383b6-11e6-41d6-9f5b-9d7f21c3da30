import nodemailer from "nodemailer";
import { hasuraQuery } from "@/utils/db";

interface MailConfig {
  smtp_host: string;
  smtp_port: number;
  smtp_user: string;
  smtp_pass: string;
  smtp_secure: boolean;
}

interface EmailPayload {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string; // Optional custom from address
}

/**
 * Send email using configured SMTP settings from database
 * @param payload Email payload with recipient, subject, and content
 * @throws Error if no mail configuration found or sending fails
 */
export async function sendEmail(payload: EmailPayload): Promise<void> {
  try {
    const config = await getMailConfig();

    const transporter = nodemailer.createTransport({
      host: config.smtp_host,
      port: config.smtp_port,
      secure: config.smtp_secure,
      auth: {
        user: config.smtp_user,
        pass: config.smtp_pass,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });

    await transporter.verify();

    const mailOptions = {
      from: payload.from || `"Portavio" <${config.smtp_user}>`,
      to: payload.to,
      subject: payload.subject,
      text: payload.text,
      html: payload.html,
    };

    const info = await transporter.sendMail(mailOptions);

    console.log("Email sent successfully:", {
      messageId: info.messageId,
      to: payload.to,
      subject: payload.subject,
    });
  } catch (error) {
    console.error("Failed to send email:", error);
    throw new Error(
      `Eroare la trimiterea email-ului: ${
        error instanceof Error ? error.message : "Eroare necunoscută"
      }`
    );
  }
}

/**
 * Get mail configuration from database
 * Fetches the default mail configuration or the first available one
 */
async function getMailConfig(): Promise<MailConfig> {
  try {
    const query = `
      query GetMailConfig {
        ptvuser_mail_config(
          where: { is_default: { _eq: true } }
          limit: 1
        ) {
          smtp_host
          smtp_port
          smtp_user
          smtp_pass
          smtp_secure
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_mail_config: MailConfig[];
    }>(query);

    let config = result.ptvuser_mail_config?.[0];

    // If no default config found, get the first available one
    if (!config) {
      const fallbackQuery = `
        query GetFirstMailConfig {
          ptvuser_mail_config(limit: 1) {
            smtp_host
            smtp_port
            smtp_user
            smtp_pass
            smtp_secure
          }
        }
      `;

      const fallbackResult = await hasuraQuery<{
        ptvuser_mail_config: MailConfig[];
      }>(fallbackQuery);

      config = fallbackResult.ptvuser_mail_config?.[0];
    }

    if (!config) {
      throw new Error(
        "Nu s-a găsit nicio configurație de email în baza de date"
      );
    }

    if (!config.smtp_host || !config.smtp_user || !config.smtp_pass) {
      throw new Error("Configurația de email este incompletă");
    }

    return config;
  } catch (error) {
    console.error("Failed to get mail configuration:", error);
    throw new Error(
      `Eroare la obținerea configurației de email: ${
        error instanceof Error ? error.message : "Eroare necunoscută"
      }`
    );
  }
}

/**
 * Send a test email to verify configuration
 * @param testEmail Email address to send test email to
 */
export async function sendTestEmail(testEmail: string): Promise<void> {
  const testPayload: EmailPayload = {
    to: testEmail,
    subject: "Test Email - Portavio",
    text: "Acesta este un email de test de la Portavio. Dacă primiți acest mesaj, configurația SMTP funcționează corect!",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #ff7f50;">Test Email - Portavio</h2>
        <p>Acesta este un email de test de la <strong>Portavio</strong>.</p>
        <p>Dacă primiți acest mesaj, configurația SMTP funcționează corect!</p>
        <hr style="border: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          Trimis la: ${new Date().toLocaleString("ro-RO")}
        </p>
      </div>
    `,
  };

  await sendEmail(testPayload);
}

/**
 * Send password reset email with professional Romanian template
 * @param userEmail Email address to send reset email to
 * @param userName User's display name (optional)
 * @param resetToken JWT token for password reset
 * @param resetUrl Complete URL for password reset
 */
export async function sendPasswordResetEmail(
  userEmail: string,
  userName: string,
  resetToken: string,
  resetUrl: string
): Promise<void> {
  const displayName = userName || userEmail.split("@")[0];

  const htmlContent = `
    <!DOCTYPE html>
    <html lang="ro">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Resetare Parolă - Portavio</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc;">
        <tr>
          <td align="center" style="padding: 40px 20px;">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header with Brand -->
              <tr>
                <td align="center" style="padding: 40px 40px 30px 40px; background-color: #ff7f50; border-radius: 12px 12px 0 0;">
                  <div style="display: inline-block; padding: 16px 24px; background-color: rgba(255, 255, 255, 0.2); border-radius: 8px; margin-bottom: 20px; border: 2px solid rgba(255, 255, 255, 0.3);">
                    <h1 style="color: #ffffff !important; font-size: 24px; font-weight: 700; margin: 0; text-align: center; letter-spacing: 1px; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">PORTAVIO</h1>
                  </div>
                  <h2 style="color: #ffffff !important; font-size: 28px; font-weight: 700; margin: 0; text-align: center; line-height: 1.2; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">🔐 Resetare Parolă</h2>
                  <p style="color: #ffffff !important; font-size: 16px; margin: 8px 0 0 0; text-align: center; opacity: 0.95;">Pentru contul tău Portavio</p>
                </td>
              </tr>

              <!-- Main Content -->
              <tr>
                <td style="padding: 40px;">
                  <h2 style="color: #1a202c; font-size: 24px; font-weight: 600; margin: 0 0 20px 0; text-align: left;">Salut, ${displayName}!</h2>

                  <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    Am primit o solicitare pentru resetarea parolei contului tău <strong>Portavio</strong>.
                  </p>

                  <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0 0 30px 0;">
                    ⏰ Acest link va expira <strong>într-o oră</strong> din motive de securitate.
                  </p>

                  <!-- CTA Button -->
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center" style="padding: 20px 0;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                          <tr>
                            <td align="center" style="background-color: #ff7f50; border-radius: 6px; padding: 14px 28px;">
                              <a href="${resetUrl}" style="color: white; font-size: 16px; font-weight: bold; text-decoration: none; display: block;">
                                🔑 Resetează Parola
                              </a>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>

                  <!-- Security Notice -->
                  <div style="background-color: #fef5e7; border-left: 4px solid #f6ad55; padding: 16px; border-radius: 6px; margin: 30px 0;">
                    <p style="color: #744210; font-size: 14px; margin: 0; line-height: 1.5;">
                      <strong>🔒 Notă de securitate:</strong> Dacă nu ai solicitat resetarea parolei, poți ignora acest email în siguranță. Link-ul va expira în 1 oră.
                    </p>
                  </div>

                  <!-- Alternative Link -->
                  <div style="background-color: #f7fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <p style="color: #4a5568; font-size: 13px; margin: 0 0 10px 0; text-align: center;">
                      <strong>Butonul nu funcționează?</strong> Copiază și lipește acest link în browser:
                    </p>
                    <p style="color: #ff7f50; font-size: 12px; margin: 0; text-align: center; word-break: break-all;">
                      <a href="${resetUrl}" style="color: #ff7f50; text-decoration: underline;">${resetUrl}</a>
                    </p>
                  </div>
                </td>
              </tr>

              <!-- Footer -->
              <tr>
                <td style="padding: 30px 40px; background-color: #f7fafc; border-radius: 0 0 12px 12px; border-top: 1px solid #e2e8f0;">
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center">
                        <p style="color: #718096; font-size: 12px; margin: 0 0 8px 0; text-align: center;">
                          © ${new Date().getFullYear()} <strong>Portavio</strong>. Toate drepturile rezervate.
                        </p>
                        <p style="color: #a0aec0; font-size: 11px; margin: 0; text-align: center;">
                          Platforma ta de urmărire a portofoliului de investiții
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `;

  const textContent = `
Resetare Parolă - Portavio

Salut, ${displayName}!

Am primit o solicitare pentru resetarea parolei contului tău Portavio.

Pentru a-ți reseta parola, accesează următorul link:
${resetUrl}

NOTĂ DE SECURITATE:
- Acest link este valabil doar 1 oră
- Dacă nu ai solicitat resetarea parolei, ignoră acest email
- Nu împărtăși acest link cu nimeni

Dacă întâmpini probleme, contactează-<NAME_EMAIL>

Cu respect,
Echipa Portavio

© ${new Date().getFullYear()} Portavio. Toate drepturile rezervate.
  `;

  await sendEmail({
    to: userEmail,
    subject: "🔐 Resetare Parolă - Portavio",
    html: htmlContent,
    text: textContent,
  });

  console.log("Password reset email sent successfully:", {
    to: userEmail,
    resetToken: resetToken.substring(0, 10) + "...", // Log only first 10 chars for security
  });
}
