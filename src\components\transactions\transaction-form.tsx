"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker-simple";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { TickerSearch } from "@/components/transactions/ticker-search";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  TransactionFormData,
  getDefaultTransactionFormData,
  transactionFormSchema,
} from "@/lib/transaction-schemas";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatDate } from "date-fns";
import { AlertCircle, Plus, Minus } from "lucide-react";
import { useState, useCallback, useEffect } from "react";
import { useForm, useWatch } from "react-hook-form";
import { toast } from "sonner";

interface TransactionFormProps {
  portfolios: Portfolio[];
  onSubmit: (data: TransactionFormData) => Promise<void>;
  isLoading?: boolean;
  defaultPortfolioId?: string;
}

export function TransactionForm({
  portfolios,
  onSubmit,
  isLoading = false,
  defaultPortfolioId,
}: TransactionFormProps) {
  const [error, setError] = useState<string | null>(null);
  const [newAssetInfo, setNewAssetInfo] = useState<any>(null);
  const [isCreatingAsset, setIsCreatingAsset] = useState(false);
  const [assetCreationProgress, setAssetCreationProgress] = useState<{
    step: string;
    message: string;
    progress: number;
    details?: string;
  } | null>(null);

  // State for selected asset currency information
  const [selectedAssetCurrency, setSelectedAssetCurrency] = useState<
    string | null
  >(null);

  // State for portfolio assets (for SELL transactions)
  const [portfolioAssets, setPortfolioAssets] = useState<any[]>([]);
  const [portfolioAssetsLoading, setPortfolioAssetsLoading] = useState(false);
  const [portfolioAssetsError, setPortfolioAssetsError] = useState<
    string | null
  >(null);

  const form = useForm<TransactionFormData>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: getDefaultTransactionFormData(
      defaultPortfolioId || portfolios[0]?.id
    ),
  });

  // Use useWatch to avoid infinite re-renders
  const watchedPortfolioId = useWatch({
    control: form.control,
    name: "portfolioId",
  });

  const watchedTransactionType = useWatch({
    control: form.control,
    name: "type",
  });

  const watchedTicker = useWatch({
    control: form.control,
    name: "ticker",
  });

  const showPortfolioSelector = portfolios.length > 1;

  // Fetch portfolio assets when transaction type is SELL
  useEffect(() => {
    if (watchedTransactionType === "SELL" && watchedPortfolioId) {
      const fetchPortfolioAssets = async () => {
        try {
          setPortfolioAssetsLoading(true);
          setPortfolioAssetsError(null);

          // Fetch portfolio assets with full information including currency
          const assetsResponse = await fetch(
            `/api/portfolios/${watchedPortfolioId}/assets`
          );
          if (!assetsResponse.ok) {
            throw new Error("Nu s-au putut încărca activele din portofoliu");
          }
          const assetsData = await assetsResponse.json();
          const assets = assetsData.assets || [];

          setPortfolioAssets(assets);
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : "A apărut o eroare";
          setPortfolioAssetsError(errorMessage);
          console.error("Error fetching portfolio assets:", error);
        } finally {
          setPortfolioAssetsLoading(false);
        }
      };

      fetchPortfolioAssets();
    } else {
      // Reset portfolio assets when not needed
      setPortfolioAssets([]);
      setPortfolioAssetsError(null);
    }
  }, [watchedTransactionType, watchedPortfolioId]);

  // Handle asset selection from TickerSearch
  const handleAssetSelected = useCallback((asset: any) => {
    if (asset && asset.currency) {
      setSelectedAssetCurrency(asset.currency);
    } else {
      setSelectedAssetCurrency(null);
    }
  }, []);

  // Update selected asset currency when ticker changes (for manual entry or edge cases)
  useEffect(() => {
    if (watchedTicker) {
      // Check if we have currency info from newly created asset
      if (
        newAssetInfo &&
        newAssetInfo.ticker === watchedTicker &&
        newAssetInfo.currency
      ) {
        setSelectedAssetCurrency(
          newAssetInfo.currency.code || newAssetInfo.currency
        );
        return;
      }
    } else {
      setSelectedAssetCurrency(null);
    }
  }, [
    watchedTicker,
    watchedTransactionType,
    newAssetInfo,
    selectedAssetCurrency,
  ]);

  // Handle asset creation progress
  const handleAssetCreationProgress = useCallback(
    (progress: {
      step: string;
      message: string;
      progress: number;
      details?: string;
    }) => {
      setAssetCreationProgress(progress);
      setIsCreatingAsset(
        progress.step !== "completed" && progress.step !== "error"
      );
    },
    []
  );

  // Handle asset creation callback
  const handleAssetCreated = useCallback((asset: any) => {
    setNewAssetInfo(asset);
    setIsCreatingAsset(false);
    setAssetCreationProgress(null);

    // Set currency from newly created asset
    if (asset.currency) {
      setSelectedAssetCurrency(asset.currency.code || asset.currency);
    }

    // Show success message with asset details
    const assetName = asset.name || asset.ticker;
    const companyInfo = asset.company ? ` (${asset.company})` : "";
    toast.success(
      `Activul ${asset.ticker} - ${assetName}${companyInfo} a fost adăugat cu succes!`,
      {
        description: "Poți acum să continui cu tranzacția.",
        duration: 5000,
      }
    );
  }, []);

  const handleFormSubmit = async (data: TransactionFormData) => {
    setError(null);

    try {
      await onSubmit(data);

      form.reset(getDefaultTransactionFormData(data.portfolioId));
      setSelectedAssetCurrency(null);
      setNewAssetInfo(null);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  const handleTransactionTypeChange = (value: string) => {
    form.setValue("type", value as TransactionFormData["type"]);
    form.setValue("ticker", "");
    setSelectedAssetCurrency(null);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleFormSubmit)}
            className="space-y-6"
          >
            {error && (
              <div className="rounded-md bg-red-50 p-3 text-sm text-red-600 dark:text-red-400 dark:bg-red-900/20 flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            )}

            {/* Transaction Type - BUY or SELL */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipul Tranzacției</FormLabel>
                  <Select
                    onValueChange={handleTransactionTypeChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger
                        className={
                          form.formState.errors.type
                            ? "border-red-500 dark:border-red-400"
                            : ""
                        }
                      >
                        <SelectValue placeholder="Selectează tipul tranzacției" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="BUY">
                        <div className="flex items-center gap-2">
                          <Plus className="h-4 w-4 text-green-600" />
                          <span>BUY</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="SELL">
                        <div className="flex items-center gap-2">
                          <Minus className="h-4 w-4 text-red-600" />
                          <span>SELL</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Portfolio Selection - Only show if user has multiple portfolios */}
            {showPortfolioSelector && (
              <FormField
                control={form.control}
                name="portfolioId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Portofoliu</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger
                          className={
                            form.formState.errors.portfolioId
                              ? "border-red-500 dark:border-red-400"
                              : ""
                          }
                        >
                          <SelectValue placeholder="Selectează portofoliul" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {portfolios.map((portfolio) => (
                          <SelectItem key={portfolio.id} value={portfolio.id}>
                            {portfolio.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage className="text-red-600 dark:text-red-400" />
                  </FormItem>
                )}
              />
            )}

            {/* Hidden portfolio field if only one portfolio */}
            {!showPortfolioSelector && portfolios.length === 1 && (
              <FormField
                control={form.control}
                name="portfolioId"
                render={({ field }) => (
                  <input type="hidden" {...field} value={portfolios[0].id} />
                )}
              />
            )}

            {/* Ticker Symbol */}
            <FormField
              control={form.control}
              name="ticker"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Simbol (Ticker)</FormLabel>
                  <FormControl>
                    <TickerSearch
                      value={field.value}
                      onValueChange={field.onChange}
                      onAssetCreated={handleAssetCreated}
                      onAssetCreationProgress={handleAssetCreationProgress}
                      onAssetSelected={handleAssetSelected}
                      placeholder="Caută după simbol, nume sau companie..."
                      disabled={
                        isLoading || isCreatingAsset || portfolioAssetsLoading
                      }
                      error={
                        !!form.formState.errors.ticker || !!portfolioAssetsError
                      }
                      assets={
                        watchedTransactionType === "SELL"
                          ? portfolioAssets
                          : undefined
                      }
                      allowAssetCreation={watchedTransactionType !== "SELL"}
                      allowCustomValue={watchedTransactionType !== "SELL"}
                    />
                  </FormControl>
                  <FormDescription>
                    Caută și selectează un activ
                  </FormDescription>

                  {/* Show asset creation progress */}
                  {isCreatingAsset && assetCreationProgress && (
                    <div className="rounded-md bg-blue-50 p-4 text-sm text-blue-600 dark:text-blue-400 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center gap-3">
                        <div className="flex-shrink-0">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 dark:border-blue-400"></div>
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">
                            {assetCreationProgress.message}
                          </div>
                          {assetCreationProgress.details && (
                            <div className="text-xs mt-1 text-blue-500 dark:text-blue-300">
                              {assetCreationProgress.details}
                            </div>
                          )}
                          <div className="mt-2">
                            <div className="bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                              <div
                                className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300 ease-out"
                                style={{
                                  width: `${assetCreationProgress.progress}%`,
                                }}
                              ></div>
                            </div>
                            <div className="text-xs mt-1 text-blue-500 dark:text-blue-300">
                              {assetCreationProgress.progress}% completat
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Show info about newly created asset */}
                  {newAssetInfo && field.value === newAssetInfo.ticker && (
                    <div className="rounded-md bg-green-50 border border-green-200 dark:border-green-800 p-3 text-sm text-green-600 dark:text-green-400 dark:bg-green-900/20 flex items-start gap-2">
                      <div className="flex-shrink-0 mt-0.5">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <div className="font-medium">Activ nou creat!</div>
                        <div className="mt-1">
                          <strong>{newAssetInfo.ticker}</strong> -{" "}
                          {newAssetInfo.name}
                          {newAssetInfo.company && (
                            <span className="text-green-500 dark:text-green-300">
                              {" "}
                              ({newAssetInfo.company})
                            </span>
                          )}
                        </div>
                        {newAssetInfo.currency && (
                          <div className="text-xs mt-1 text-green-500 dark:text-green-300">
                            Monedă: {newAssetInfo.currency.code}
                            {newAssetInfo.currency.name &&
                              ` (${newAssetInfo.currency.name})`}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Transaction Date */}
            <FormField
              control={form.control}
              name="transactionDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data Tranzacției</FormLabel>
                  <FormControl>
                    <DatePicker
                      value={field.value ? new Date(field.value) : undefined}
                      onChange={(date) => {
                        const dateStr = date
                          ? formatDate(date, "yyyy-MM-dd")
                          : "";
                        field.onChange(dateStr);
                      }}
                      maxDate={(() => {
                        const yesterday = new Date();
                        yesterday.setDate(yesterday.getDate() - 1);
                        return yesterday;
                      })()}
                      error={!!form.formState.errors.transactionDate}
                      disabled={isLoading}
                      placeholder="Selectează data tranzacției"
                    />
                  </FormControl>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Price */}
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preț per unitate</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.00000001"
                        min="0"
                        max="999999999"
                        placeholder="ex. 150.25 sau 0.001"
                        value={field.value?.toString() || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;
                          if (value === "") {
                            field.onChange(0);
                          } else {
                            const numValue = parseFloat(value);
                            if (!isNaN(numValue)) {
                              field.onChange(numValue);
                            }
                          }
                        }}
                        onWheel={(e) => {
                          e.currentTarget.blur();
                        }}
                        className={
                          form.formState.errors.price
                            ? "border-red-500 dark:border-red-400"
                            : ""
                        }
                        disabled={isLoading}
                      />
                      {selectedAssetCurrency && (
                        <div className="absolute right-8 top-1/2 -translate-y-1/2 text-sm text-muted-foreground font-medium">
                          {selectedAssetCurrency}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Prețul plătit per unitate (suportă până la 8 zecimale)
                    {selectedAssetCurrency && ` în ${selectedAssetCurrency}`}
                  </FormDescription>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Quantity */}
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cantitate</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.00000001"
                      min="0"
                      max="999999999"
                      placeholder="ex. 10 sau 10.5"
                      value={field.value?.toString() || ""}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      onBlur={(e) => {
                        const value = e.target.value;
                        if (value === "") {
                          field.onChange(0);
                        } else {
                          const numValue = parseFloat(value);
                          if (!isNaN(numValue)) {
                            field.onChange(numValue);
                          }
                        }
                      }}
                      onWheel={(e) => {
                        e.currentTarget.blur();
                      }}
                      className={
                        form.formState.errors.quantity
                          ? "border-red-500 dark:border-red-400"
                          : ""
                      }
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Suportă acțiuni fracționare (până la 8 zecimale)
                  </FormDescription>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Transaction Fee (Optional) */}
            <FormField
              control={form.control}
              name="transaction_fee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comision tranzacție (Opțional)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.00000001"
                        min="0"
                        max="999999999"
                        placeholder="0,00"
                        value={field.value?.toString() || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;
                          if (value === "") {
                            field.onChange(0);
                          } else {
                            const numValue = parseFloat(value);
                            if (!isNaN(numValue)) {
                              field.onChange(numValue);
                            }
                          }
                        }}
                        onWheel={(e) => {
                          e.currentTarget.blur();
                        }}
                        className={
                          form.formState.errors.transaction_fee
                            ? "border-red-500 dark:border-red-400"
                            : ""
                        }
                        disabled={isLoading}
                      />
                      {selectedAssetCurrency && (
                        <div className="absolute right-8 top-1/2 -translate-y-1/2 text-sm text-muted-foreground font-medium">
                          {selectedAssetCurrency}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Comisionul plătit pentru această tranzacție
                  </FormDescription>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Notes (Optional) */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Note (Opțional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Adaugă note despre această tranzacție..."
                      {...field}
                      className={
                        form.formState.errors.notes
                          ? "border-red-500 dark:border-red-400"
                          : ""
                      }
                      disabled={isLoading}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button
                type="submit"
                disabled={
                  !form.formState.isDirty || isLoading || isCreatingAsset
                }
                className="min-w-[150px]"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Se salvează...
                  </div>
                ) : isCreatingAsset ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Se creează activul...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Adaugă Tranzacția
                  </div>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
